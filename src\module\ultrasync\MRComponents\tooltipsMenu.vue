<template>
	<div class="tooltips-menu" v-if="menuDatas.length>0">
        <div class="first-wrap" v-if="firstArr.length>0">
            <template v-for="(item,index) in transfer" >
                <van-button
                    class="tooltips-menu-item"
                    type="default"
                    :key="index"
                    @click="clickItem(item)"
                    v-if="firstArr.includes(item.key)"
                    :style="getButtonStyle($t(item.name))">
                        {{ $t(item.name) }}
                </van-button>
            </template>
        </div>
        <div class="second-wrap" v-if="secondArr.length>0">
            <template v-for="(item,index) in transfer">
                <van-button
                    class="tooltips-menu-item"
                    type="default"
                    :key="index"
                    @click="clickItem(item)"
                    v-if="secondArr.includes(item.key)"
                    :style="getButtonStyle($t(item.name))">
                        {{ $t(item.name) }}
                </van-button>
            </template>
        </div>
        <div class="third-wrap" v-if="thirdArr.length>0">
            <template v-for="(item,index) in transfer">
                <van-button
                    class="tooltips-menu-item"
                    type="default"
                    :key="index"
                    @click="clickItem(item)"
                    v-if="thirdArr.includes(item.key)"
                    :style="getButtonStyle($t(item.name))">
                        {{ $t(item.name) }}
                </van-button>
            </template>
        </div>
	</div>
</template>
<script>
import { Button } from 'vant'
import base from '../lib/base'
import {getLanguage} from '@/common/i18n'
import { MENU_ITEMS, MENU_ITEM_CONFIG } from '../lib/constants.js'
export default {
    components:{
        VanButton: Button,
    },
    mixins: [base],
    data(){
        return {
            transfer: MENU_ITEM_CONFIG,
            firstArr:[],
            secondArr:[],
            thirdArr:[],
        }
    },
    props:{
        menuDatas:{
            type:Array,
            default:()=>{
                return []
            }
        },
    },
    created(){
        this.firstArr = []
        this.secondArr = []
        this.thirdArr = []
        if(getLanguage() ==='CN'){
            this.menuDatas.forEach((item,index)=>{
                if(index<4){
                    this.firstArr.push(item)
                }else if(index>=4&&index<8){
                    this.secondArr.push(item)
                }else if(index>=8){
                    this.thirdArr.push(item)
                }
            })
        }else{
            this.menuDatas.forEach((item,index)=>{
                if(index<3){
                    this.firstArr.push(item)
                }else if(index>=3&&index<6){
                    this.secondArr.push(item)
                }else if(index>=6){
                    this.thirdArr.push(item)
                }
            })
        }



    },
    beforeDestroy(){
        this.firstArr = []
        this.secondArr = []
        this.thirdArr = []
    },
    methods:{
        clickItem(item){
            // 使用配置中的事件名称，避免魔法值
            if (item.event) {
                this.$emit(item.event);
            } else {
                console.warn('Menu item has no event configured:', item);
            }
        },
        getButtonStyle(text) {
            // 根据文本长度动态计算按钮宽度，确保不超出屏幕
            const textLength = text.length;
            const isEnglish = /^[a-zA-Z\s]+$/.test(text);

            // 基础宽度设置，适应移动端屏幕
            let minWidth = '3rem';
            let maxWidth = '5rem';

            if (isEnglish) {
                // 英文文本需要更多空间，但限制在合理范围内
                if (textLength > 15) {
                    minWidth = '3.5rem';
                    maxWidth = '5.5rem';
                } else if (textLength > 10) {
                    minWidth = '3.2rem';
                    maxWidth = '5.2rem';
                } else if (textLength > 6) {
                    minWidth = '3rem';
                    maxWidth = '5rem';
                }
            } else {
                // 中文文本相对紧凑
                if (textLength > 6) {
                    minWidth = '3.5rem';
                    maxWidth = '5rem';
                } else if (textLength > 4) {
                    minWidth = '3rem';
                    maxWidth = '4.5rem';
                }
            }

            return {
                minWidth,
                maxWidth
            };
        }
    }
}
</script>
<style lang="scss">
.tooltips-menu{
    background-color: #060607;
    position: relative;
    flex: 1;
    border-radius: 5px;
    padding: 0 0;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    align-items: flex-start;
    max-width: min(18rem, calc(100vw - 2rem));
    width: fit-content;
    .tooltips-menu-item{
        padding: 0.3rem 0.4rem;
        font-size: 0.7rem;
        background-color: #060607;
        color: #fff;
        border: 0;
        text-overflow: unset;
        flex: 1;
        height: auto;
        min-height: 2rem;
        .van-button__text{
            white-space: pre-wrap;
            word-break: break-word;
            line-height: 1.2;
            width: 100%;
            text-align: center;
        }
    }
}
.first-wrap{
    display: flex;
    width: 100%;
    gap: 0;
    align-items: stretch;
}
.second-wrap,.third-wrap{
    border-top: 1px solid #757575;
    width: 100%;
    display: flex;
    gap: 0;
    align-items: stretch;
}
</style>
