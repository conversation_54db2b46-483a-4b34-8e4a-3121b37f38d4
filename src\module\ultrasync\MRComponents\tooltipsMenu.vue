<template>
	<div class="tooltips-menu" v-if="menuDatas.length>0">
        <div class="first-wrap" v-if="firstArr.length>0">
            <template v-for="(item,index) in transfer" >
                <van-button class="tooltips-menu-item" type="default" :key="index"   @click="clickItem(item)" v-if="firstArr.includes(item.key)">
                        {{ $t(item.name) }}
                </van-button>
            </template>
        </div>
        <div class="second-wrap" v-if="secondArr.length>0">
            <template v-for="(item,index) in transfer">
                <van-button class="tooltips-menu-item" type="default" :key="index"   @click="clickItem(item)" v-if="secondArr.includes(item.key)">
                        {{ $t(item.name) }}
                </van-button>
            </template>
        </div>
        <div class="third-wrap" v-if="thirdArr.length>0">
            <template v-for="(item,index) in transfer">
                <van-button class="tooltips-menu-item" type="default" :key="index"   @click="clickItem(item)" v-if="thirdArr.includes(item.key)">
                        {{ $t(item.name) }}
                </van-button>
            </template>
        </div>
	</div>
</template>
<script>
import { Button } from 'vant'
import base from '../lib/base'
import {getLanguage} from '@/common/i18n'
import { MENU_ITEMS, MENU_ITEM_CONFIG } from '../lib/constants.js'
export default {
    components:{
        VanButton: Button,
    },
    mixins: [base],
    data(){
        return {
            transfer: MENU_ITEM_CONFIG,
            firstArr:[],
            secondArr:[],
            thirdArr:[],
        }
    },
    props:{
        menuDatas:{
            type:Array,
            default:()=>{
                return []
            }
        },
    },
    created(){
        this.firstArr = []
        this.secondArr = []
        this.thirdArr = []
        if(getLanguage() ==='CN'){
            this.menuDatas.forEach((item,index)=>{
                if(index<4){
                    this.firstArr.push(item)
                }else if(index>=4&&index<8){
                    this.secondArr.push(item)
                }else if(index>=8){
                    this.thirdArr.push(item)
                }
            })
        }else{
            this.menuDatas.forEach((item,index)=>{
                if(index<3){
                    this.firstArr.push(item)
                }else if(index>=3&&index<6){
                    this.secondArr.push(item)
                }else if(index>=6){
                    this.thirdArr.push(item)
                }
            })
        }



    },
    beforeDestroy(){
        this.firstArr = []
        this.secondArr = []
        this.thirdArr = []
    },
    methods:{
        clickItem(item){
            // 使用配置中的事件名称，避免魔法值
            if (item.event) {
                this.$emit(item.event);
            } else {
                console.warn('Menu item has no event configured:', item);
            }
        }
    }
}
</script>
<style lang="scss">
.tooltips-menu{
    background-color: #060607;
    position: relative;
    flex: 1;
    border-radius: 5px;
    padding: 0 0;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    align-items: flex-start;
    max-width: 17rem;
    .tooltips-menu-item{
        padding: 0.4rem;
        font-size: 0.7rem;
        background-color: #060607;
        color: #fff;
        border: 0;
        max-width: 6rem;
        text-overflow:unset;
        .van-button__text{
            white-space: pre-wrap;
            // text-overflow: ellipsis;
            overflow: hidden;
            width: 100%;
        }
    }
}
.first-wrap{
    white-space: nowrap;
    display: flex;
}
.second-wrap,.third-wrap{
    border-top: 1px solid #757575;
    width: 100%;
    white-space: nowrap;
    display: flex;
}
</style>
